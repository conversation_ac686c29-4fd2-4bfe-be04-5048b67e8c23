'use client';
import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Tooltip,
  Popover,
  Checkbox,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import AddIcon from '@mui/icons-material/Add';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { useRouter } from 'next/navigation';
import {
  fetchFromStorage,
  removeFromStorage,
  saveToStorage,
} from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import NoDataView from '@/components/UI/NoDataView';
import CommonUserDetails from '@/components/UI/CommonUserDetails/index';
import FilterListIcon from '@mui/icons-material/FilterList';
import useRoleList from '@/hooks/useRoleList';
import CustomOrgPagination from '@/components/UI/customPagination';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';
import DownloadIcon from '@mui/icons-material/Download';
import { staticOptions } from '@/helper/common/staticOptions';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import SettingsIcon from '@mui/icons-material/Settings';
import DownloadList from '@/components/Users/<USER>/DownloadList';
import ExportStatusIndicator from '@/components/Users/<USER>/DownloadField/components/ExportProgress';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import moment from 'moment';
import '../reports.scss';
import '@/components/Users/<USER>/staff.scss';

export default function StaffUserReports() {
  const { authState, setUserdata } = useContext(AuthContext);
  const router = useRouter();

  const [staffList, setStaffList] = useState([]);
  const [loader, setLoader] = useState(false);
  const [filters, setFilters] = useState({});
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchValue, setSearchValue] = useState('');

  // Modal states
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [viewingStaff, setViewingStaff] = useState(null);
  const [editingStaff, setEditingStaff] = useState(null);

  // Filter options state
  const [branchOptions, setBranchOptions] = useState([]);
  const [departmentOptions, setDepartmentOptions] = useState([]);
  const [roleOptions, setRoleOptions] = useState([]);

  // Filter fields for staff reports
  const filterFields = [
    {
      name: 'search',
      type: 'text',
      placeholder: 'Search by name, email, or employment number',
      label: 'Search',
    },
    {
      name: 'branch',
      type: 'select',
      placeholder: 'Select Branch',
      label: 'Branch',
      options: branchOptions,
    },
    {
      name: 'department',
      type: 'select',
      placeholder: 'Select Department',
      label: 'Department',
      options: departmentOptions,
    },
    {
      name: 'role',
      type: 'select',
      placeholder: 'Select Role',
      label: 'Role',
      options: roleOptions,
    },
    {
      name: 'status',
      type: 'select',
      placeholder: 'Select Status',
      label: 'Status',
      options: staticOptions.USER_STATUS_OPTIONS,
    },
    {
      name: 'trainingStatus',
      type: 'select',
      placeholder: 'Select Training Status',
      label: 'Training Status',
      options: staticOptions.TRAINING_FILTER_STATUS,
    },
    {
      name: 'contractStatus',
      type: 'select',
      placeholder: 'Select Contract Status',
      label: 'Contract Status',
      options: staticOptions.CONTRACT_FILTER_STATUS,
    },
    {
      name: 'dateRange',
      type: 'dateRange',
      label: 'Date Range',
      placeholder: 'Select date range',
    },
  ];

  // Menu items for action dropdown
  const getActionMenuItems = () => {
    const menuItems = [
      {
        label: 'View',
        icon: <Icon name="Eye" size={16} />,
        onClick: (_, row) => {
          setViewingStaff(row?.id);
        },
      },
    ];

    // Add Edit option if user has permission
    if (authState?.UserPermission?.staff === 2) {
      menuItems.push({
        label: 'Edit',
        icon: <Icon name="Edit" size={16} />,
        onClick: (_, row) => {
          setEditingStaff(row?.id);
        },
      });
    }

    // Add Delete option if user has permission
    if (authState?.UserPermission?.staff === 2) {
      menuItems.push({
        label: 'Delete',
        icon: <Icon name="Trash2" size={16} />,
        onClick: (_, row) => {
          setDeleteId(row?.id);
          setDeleteDialogOpen(true);
        },
      });
    }

    return menuItems;
  };

  // CommonTable columns
  const columns = [
    {
      header: 'ID',
      accessor: 'employment_number',
      sortable: false,
      renderCell: (value) => (value ? value : '-'),
    },
    {
      header: 'User',
      accessor: 'user_full_name',
      sortable: false,
      renderCell: (_, row) => (
        <CommonUserDetails
          userData={row}
          searchValue={searchValue}
          page={page}
          rowsPerPage={rowsPerPage}
          setUserdata={setUserdata}
          authState={authState}
          navigationProps={{ staff: true }}
        />
      ),
    },
    {
      header: 'Branch / Dep.',
      accessor: 'branch_department',
      sortable: false,
      renderCell: (_, row) => (
        <Box>
          <Typography variant="body2" className="fw600">
            {row?.assign_branch_ids?.[0]?.branch_name || '-'}
          </Typography>
          <Typography variant="caption" color="textSecondary">
            {row?.department?.department_name || '-'}
          </Typography>
        </Box>
      ),
    },
    {
      header: 'Joining Date',
      accessor: 'createdAt',
      sortable: false,
      renderCell: (value) => DateFormat(value, 'dates'),
    },
    {
      header: 'Profile Status',
      accessor: 'status',
      sortable: false,
      renderCell: (value) => (
        <Box
          className={`status-chip ${value === 'active' ? 'active' : value === 'pending' ? 'pending' : 'inactive'}`}
        >
          {value || '-'}
        </Box>
      ),
    },
    {
      header: 'Training Status',
      accessor: 'user_track_status',
      sortable: false,
      renderCell: (value) => (
        <Box
          className={`status-chip ${value === 'pending' ? 'pending' : value === 'ongoing' ? 'ongoing' : 'pending'}`}
        >
          {value || '-'}
        </Box>
      ),
    },
    {
      header: 'Contract Status',
      accessor: 'contract_status',
      sortable: false,
      renderCell: (value) => (
        <Box
          className={`status-chip ${value === 'pending' ? 'pending' : value === 'awaiting_signature' ? 'awaiting' : 'pending'}`}
        >
          {value || '-'}
        </Box>
      ),
    },
  ];

  // Handle filter application with date range support
  const handleApplyFilters = (filterData) => {
    setFilters(filterData);
    setPage(1);
    setSearchValue(filterData?.search || '');

    const startDate = filterData?.dateRange?.[0] || '';
    const endDate = filterData?.dateRange?.[1] || '';

    getStaffList(
      filterData?.search || '',
      1,
      filterData?.branch || '',
      filterData?.role || '',
      filterData?.department || '',
      filterData?.status || '',
      filterData?.trainingStatus || '',
      filterData?.contractStatus || '',
      rowsPerPage,
      startDate,
      endDate
    );
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPage(newPage);
    getStaffList(
      searchValue,
      newPage,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      filters?.status || '',
      filters?.trainingStatus || '',
      filters?.contractStatus || '',
      rowsPerPage
    );
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    getStaffList(
      searchValue,
      1,
      filters?.branch || '',
      filters?.role || '',
      filters?.department || '',
      filters?.status || '',
      filters?.trainingStatus || '',
      filters?.contractStatus || '',
      newRowsPerPage
    );
  };

  // Delete staff member using staff service
  const deleteStaff = async (id) => {
    try {
      setLoader(true);
      const result = await staffService.deleteStaff(id);

      if (result.success) {
        setApiMessage('success', result.message);
        setDeleteDialogOpen(false);
        setDeleteId(null);
        // Refresh the list
        getStaffList(
          searchValue,
          page,
          filters?.branch || '',
          filters?.role || '',
          filters?.department || '',
          filters?.status || '',
          filters?.trainingStatus || '',
          filters?.contractStatus || '',
          rowsPerPage
        );
      } else {
        setApiMessage('error', 'Failed to delete staff member');
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Get staff list from API using staff service
  const getStaffList = async (
    search = '',
    pageNo = 1,
    branch = '',
    role = '',
    department = '',
    statusValue = '',
    trainingStatus = '',
    contractStatus = '',
    Rpp = rowsPerPage,
    startDate = '',
    endDate = ''
  ) => {
    setLoader(true);
    try {
      const result = await staffService.getStaffList(
        search,
        pageNo,
        branch,
        role,
        department,
        statusValue,
        trainingStatus,
        contractStatus,
        Rpp,
        startDate,
        endDate
      );

      if (result.success) {
        setStaffList(result.data.userList);
        setTotalCount(result.data.count);
        setPage(result.data.page);
      } else {
        setStaffList([]);
        setTotalCount(0);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setStaffList([]);
      setTotalCount(0);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Export staff list using staff service
  const exportStaffList = async (fileType = 'csv') => {
    try {
      const result = await staffService.exportStaffList(
        searchValue,
        1,
        filters?.branch || '',
        filters?.role || '',
        filters?.department || '',
        filters?.status || '',
        filters?.trainingStatus || '',
        filters?.contractStatus || '',
        totalCount,
        fileType,
        totalCount
      );

      if (result.success) {
        const url = window.URL.createObjectURL(new Blob([result.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `staff-report.${fileType}`);
        document.body.appendChild(link);
        link.click();
        link.remove();
        setApiMessage('success', 'Staff report exported successfully');
      } else {
        setApiMessage('error', 'Failed to export staff report');
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Load filter options
  const loadFilterOptions = async () => {
    try {
      const [branches, departments, roles] = await Promise.all([
        staffService.getBranchList(),
        staffService.getDepartmentList(),
        staffService.getRoleList(),
      ]);

      setBranchOptions(branches);
      setDepartmentOptions(departments);
      setRoleOptions(roles);
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  };

  // Handle close delete dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  // Handle create staff navigation
  const handleCreateStaff = () => {
    if (authState?.remaining_emp === 0) {
      setApiMessage('error', 'Staff limit reached');
    } else {
      router.push('/staff/create-staff');
    }
  };

  // Load initial data and filter options
  useEffect(() => {
    const initializeData = async () => {
      await loadFilterOptions();
      await getStaffList();
    };

    initializeData();
  }, []);

  return (
    <>
      <Box className="report-main-container">
        {viewingStaff ? (
          <StaffView
            staffId={viewingStaff}
            onBack={() => setViewingStaff(null)}
          />
        ) : editingStaff ? (
          <StaffEdit
            staffId={editingStaff}
            onBack={() => setEditingStaff(null)}
            onSuccess={() => {
              setEditingStaff(null);
              getStaffList();
            }}
          />
        ) : (
          <>
            <Box className="report-header">
              <Typography variant="h6" className="report-title">
                Staff User Reports
              </Typography>
              <Box className="report-actions">
                <CustomButton
                  title="Export CSV"
                  variant="outlined"
                  onClick={() => exportStaffList('csv')}
                  startIcon={<Icon name="Download" size={16} />}
                />
                <CustomButton
                  title="Export Excel"
                  variant="outlined"
                  onClick={() => exportStaffList('xlsx')}
                  startIcon={<Icon name="Download" size={16} />}
                />
                {authState?.UserPermission?.staff === 2 && (
                  <CustomButton
                    title="Create Staff"
                    startIcon={<AddIcon />}
                    onClick={handleCreateStaff}
                  />
                )}
              </Box>
            </Box>

            <FilterCollapse
              fields={filterFields}
              onApply={handleApplyFilters}
              buttonText="Apply Filters"
              initialValues={filters}
            />

            <Box className="report-table-container">
              {loader ? (
                <ContentLoader />
              ) : staffList && staffList?.length === 0 ? (
                <NoDataView
                  title="No Staff Records Found"
                  description="There is no staff data available at the moment."
                />
              ) : (
                <CommonTable
                  columns={columns}
                  data={staffList}
                  pageSize={rowsPerPage}
                  currentPage={page}
                  totalCount={totalCount}
                  onPageChange={handlePageChange}
                  onRowsPerPageChange={handleRowsPerPageChange}
                  actionMenuItems={getActionMenuItems}
                />
              )}
            </Box>
          </>
        )}
      </Box>

      {/* Delete Confirmation Modal */}
      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        title="Confirmation"
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <DeleteModal
            handleCancel={handleCloseDeleteDialog}
            handleConfirm={() => deleteStaff(deleteId)}
            text="Are you sure you want to delete this staff member?"
          />
        }
      />
    </>
  );
}

// Staff View Component (similar to CRView)
const StaffView = ({ staffId, onBack }) => {
  const [staffData, setStaffData] = useState(null);
  const [loader, setLoader] = useState(false);

  const getStaffDetails = async () => {
    setLoader(true);
    try {
      const result = await staffService.getStaffDetails(staffId);

      if (result.success) {
        setStaffData(result.data);
      } else {
        setApiMessage('error', 'Failed to load staff details');
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (staffId) {
      getStaffDetails();
    }
  }, [staffId]);

  return (
    <Box className="staff-view-container">
      <Box className="staff-view-header">
        <Icon
          name="ArrowLeft"
          size={20}
          onClick={onBack}
          style={{ cursor: 'pointer' }}
        />
        <Typography className="body-text fw600 pr8">
          View Staff Details
        </Typography>
      </Box>

      {loader ? (
        <ContentLoader />
      ) : !staffData ? (
        <NoDataView
          title="No Staff Found"
          description="There is no staff data available."
        />
      ) : (
        <Box className="staff-details-content">
          <CommonUserDetails
            userData={staffData}
            showAvatar={true}
            showBranch={true}
            showDate={true}
            showStatus={true}
          />

          <Box className="staff-additional-info">
            <Typography variant="h6" className="section-title">
              Additional Information
            </Typography>

            <Box className="info-grid">
              <Box className="info-item">
                <Typography variant="caption" color="textSecondary">
                  Employment Number
                </Typography>
                <Typography variant="body2">
                  {staffData?.employment_number || '-'}
                </Typography>
              </Box>

              <Box className="info-item">
                <Typography variant="caption" color="textSecondary">
                  Department
                </Typography>
                <Typography variant="body2">
                  {staffData?.department?.department_name || '-'}
                </Typography>
              </Box>

              <Box className="info-item">
                <Typography variant="caption" color="textSecondary">
                  Training Status
                </Typography>
                <Typography variant="body2">
                  {staffData?.user_track_status || '-'}
                </Typography>
              </Box>

              <Box className="info-item">
                <Typography variant="caption" color="textSecondary">
                  Contract Status
                </Typography>
                <Typography variant="body2">
                  {staffData?.contract_status || '-'}
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      )}
    </Box>
  );
};

// Staff Edit Component (similar to CRRemark)
const StaffEdit = ({ staffId, onBack }) => {
  const [staffData, setStaffData] = useState(null);
  const [loader, setLoader] = useState(false);

  const getStaffDetails = async () => {
    setLoader(true);
    try {
      const result = await staffService.getStaffDetails(staffId);

      if (result.success) {
        setStaffData(result.data);
      } else {
        setApiMessage('error', 'Failed to load staff details');
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleEditStaff = () => {
    // Navigate to edit staff page with the staff ID
    window.location.href = `/staff/edit-staff/${staffId}`;
  };

  useEffect(() => {
    if (staffId) {
      getStaffDetails();
    }
  }, [staffId]);

  return (
    <Box className="staff-edit-container">
      <Box className="staff-edit-header">
        <Icon
          name="ArrowLeft"
          size={20}
          onClick={onBack}
          style={{ cursor: 'pointer' }}
        />
        <Typography className="body-text fw600 pr8">
          Edit Staff Details
        </Typography>
      </Box>

      {loader ? (
        <ContentLoader />
      ) : !staffData ? (
        <NoDataView
          title="No Staff Found"
          description="There is no staff data available."
        />
      ) : (
        <Box className="staff-edit-content">
          <CommonUserDetails
            userData={staffData}
            showAvatar={true}
            showBranch={true}
            showDate={true}
            showStatus={true}
          />

          <Box className="staff-edit-actions">
            <CustomButton
              title="Edit Staff Details"
              onClick={handleEditStaff}
              startIcon={<Icon name="Edit" size={16} />}
            />
          </Box>
        </Box>
      )}
    </Box>
  );
};
